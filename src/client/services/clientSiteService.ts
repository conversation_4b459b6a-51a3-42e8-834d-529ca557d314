import { AxiosResponse } from "axios";
import appConfig from "appConfig";
import { ISiteGameUrlResponse } from "api/services/interfaces/iSiteServerService";
import appStore from "@stores/app-store";
import { IResponseSuccess } from "api/interfaces/response";
import clientApi from "client/services/http";
import { ClientApiResponse } from "utils/serverResponse";

export class ClientSiteService {
    /**
     * @see GamesController.getFunGameUrl
     * @see SiteService.getFunGameUrl
     * */
    static async getFunGameUrlRequest(
        gameCode: string
    ): Promise<AxiosResponse<IResponseSuccess<ISiteGameUrlResponse>>> {
        return await clientApi.get<IResponseSuccess<ISiteGameUrlResponse>>(
            appConfig.client.endpoints.api.games.getFunGameUrl.replace(":gameCode", gameCode),
            {
                withCredentials: true,
            }
        );
    }

    static async getFunGameUrl(gameCode: string): Promise<ISiteGameUrlResponse> {
        let gameUrlResponse: ISiteGameUrlResponse = {} as ISiteGameUrlResponse;
        try {
            const response = new ClientApiResponse(await ClientSiteService.getFunGameUrlRequest(gameCode)).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    gameUrlResponse = success.data as ISiteGameUrlResponse;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return gameUrlResponse;
    }
}
