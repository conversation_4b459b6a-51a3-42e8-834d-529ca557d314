import logger from "utils/logger";
import { IGamesController } from "api/controllers/interfaces/iGamesController";
import { NextApiRequest, NextApiResponse } from "next";
import appConfig from "appConfig";
import { errorCodes } from "errorCodes";
import { ServerRequest } from "utils/serverRequest";
import { validateRequestParams, validator } from "models/validatorSchemas";
import { EnumServerResponseType } from "utils/serverResponse";
import { ServerController } from "api/controllers/serverController";
import { GamesServiceInstance } from "api/services/gamesService";
import { SiteServiceInstance } from "api/services/siteService";

const log = logger("controller--games");

export class GamesController extends ServerController implements IGamesController {

    constructor(serverRequest: ServerRequest) {
        super(serverRequest);
    }

    static async handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
        try {
            const endpoints = appConfig.client.endpoints.api;
            const c = new ServerRequest(GamesController, req, res, log, appConfig.client.endpoints.api.path);
            await c.httpGet(endpoints.games.available, GamesController.prototype.getAvailable, true);
            await c.httpGet(endpoints.games.search, GamesController.prototype.getSearch, false);
            await c.httpGet(endpoints.games.roadmap, GamesController.prototype.getRoadmap, true);
            await c.httpGet(endpoints.games.playPromoGame, GamesController.prototype.getPlayPromo, true);
            await c.httpGet(endpoints.games.getMarketingMaterials, GamesController.prototype.getMarketingKit, true);
            await c.httpGet(endpoints.games.getFunGameUrl, GamesController.prototype.getFunGameUrl, true);
            await c.httpEnd();
        } catch (e) {
            log.error(e);
        }
    }

    async getAvailable(params: {}): Promise<void> {
        try {
            this.successResponse(
                await GamesServiceInstance.getAvailableGames(this.request.req, {
                    skipAuthorisation: false,
                    gameInfo: false
                }),
                EnumServerResponseType.successArray,
                GamesController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, GamesController.prototype, errorCodes.e12001001);
        }
    }

    async getSearch(params: {}): Promise<void> {
        try {
            this.successResponse(
                await GamesServiceInstance.getSearchGames(
                    this.request.req, { skipAuthorisation: true }),
                EnumServerResponseType.successArray,
                GamesController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, GamesController.prototype, errorCodes.e12003001);
        }
    }

    async getRoadmap(params: {}): Promise<void> {
        try {
            this.successResponse(
                await GamesServiceInstance.getRoadmapGames(this.request.req),
                EnumServerResponseType.successArray,
                GamesController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, GamesController.prototype, errorCodes.e12002001);
        }
    }

    async getPlayPromo(params: {}): Promise<void> {
        try {
            const response = await GamesServiceInstance.getGamePromoUrl(this.request.req);
            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                GamesController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, GamesController.prototype, errorCodes.e12004001);
        }
    }

    async getMarketingKit(params: { gameId: string }): Promise<void> {
        try {
            const data = await validateRequestParams<{ gameId: string }>(
                {
                    gameId: validator.game.gameId.required()
                },
                params
            );
            const response = await GamesServiceInstance.getMarketingKit(this.request.req, data.gameId);
            this.successResponse(
                response,
                EnumServerResponseType.successArray,
                GamesController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, GamesController.prototype, errorCodes.e12002001);
        }
    }

    async getFunGameUrl(params: { gameCode: string }): Promise<void> {
        try {
            const data = await validateRequestParams<{ gameCode: string }>(
                {
                    gameCode: validator.game.gameCode.required()
                },
                params
            );
            const response = await SiteServiceInstance.getFunGameUrl(this.request.req, data.gameCode);
            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                GamesController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, GamesController.prototype, errorCodes.e12006001);
        }
    }
}
